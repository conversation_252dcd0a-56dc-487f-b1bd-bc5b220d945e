import dayjs from "dayjs";
import { Row, Col, Space, Button, Select } from "antd";
import React, { useState, useMemo } from "react";

import { Card } from "misc/card";
import { Result } from "misc/results";
import { useParams } from "misc/hooks";
import { BatterySelector, RangeSelector } from "misc/selectors";
import { TextField, NumberField } from "misc/fields";
import { datalayerUrls } from "misc/urls";
import { StatusBoxWidget } from "misc/widgets/consumers";
import { LineChart } from "misc/charts";

const REFETCH_INTERVAL = 30000; // 30 seconds

// Available measurement types
const MEASUREMENT_TYPES = [
    { value: "soh", label: "SOH (%)" },
    { value: "safetyScore", label: "Safety Score" },
    { value: "soc", label: "SOC (%)" },
    { value: "nominalCapacity", label: "Nennkapazität (kWh)" },
    { value: "realCapacity", label: "Realkapazität (kWh)" }
];

const BatteriemessungenCard = ({ batteryId }) => {
    // State for date range and selected measurement
    const [selectedRange, setSelectedRange] = useState([dayjs().subtract(30, "days").format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")]);
    const [selectedMeasurement, setSelectedMeasurement] = useState("soh");

    // Custom date ranges for the RangeSelector
    const customRanges = [
        {
            label: "t_last_7_days",
            value: [dayjs().subtract(7, "days"), dayjs()]
        },
        {
            label: "t_last_30_days",
            value: [dayjs().subtract(30, "days"), dayjs()]
        },
        {
            label: "t_last_3_months",
            value: [dayjs().subtract(3, "months"), dayjs()]
        },
        {
            label: "t_last_6_months",
            value: [dayjs().subtract(6, "months"), dayjs()]
        },
        {
            label: "t_last_1_year",
            value: [dayjs().subtract(1, "year"), dayjs()]
        }
    ];

    // Mock data for the chart - in real implementation this would come from an API
    const chartData = useMemo(() => {
        const startDate = dayjs(selectedRange[0]);
        const endDate = dayjs(selectedRange[1]);
        const days = endDate.diff(startDate, "days");

        // Generate mock data points
        const data = [];
        for (let i = 0; i <= days; i += Math.max(1, Math.floor(days / 20))) {
            const date = startDate.add(i, "days").format("YYYY-MM-DD");
            let value;

            switch (selectedMeasurement) {
                case "soh":
                    value = 95 - (i / days) * 10 + Math.random() * 2; // Declining SOH
                    break;
                case "safetyScore":
                    value = 1.5 + Math.random() * 1.5; // Safety score between 1.5-3
                    break;
                case "soc":
                    value = 20 + Math.random() * 60; // SOC between 20-80%
                    break;
                case "nominalCapacity":
                    value = 75.2; // Constant nominal capacity
                    break;
                case "realCapacity":
                    value = 75.2 - (i / days) * 5 + Math.random() * 1; // Declining real capacity
                    break;
                default:
                    value = 50;
            }

            data.push({ date, value: Math.max(0, value) });
        }

        return data;
    }, [selectedRange, selectedMeasurement]);

    // Get measurement info
    const measurementInfo = MEASUREMENT_TYPES.find(type => type.value === selectedMeasurement);
    const yAxisTitle = measurementInfo?.label || "Value";

    return (
        <Col span={24}>
            <Card title="Batteriemessungen" size="small">
                <Space direction="vertical" size={16} style={{ width: "100%" }}>
                    {/* Controls Row */}
                    <Row gutter={[16, 16]} align="middle">
                        <Col xs={24} sm={12} md={8}>
                            <Space>
                                <TextField value="Zeitraum:" />
                                <RangeSelector
                                    value={selectedRange}
                                    onChange={setSelectedRange}
                                    ranges={customRanges}
                                    showTime={false}
                                    allowClear={false}
                                    style={{ width: 250 }}
                                />
                            </Space>
                        </Col>
                        <Col xs={24} sm={12} md={8}>
                            <Space>
                                <TextField value="Messung:" />
                                <Select
                                    value={selectedMeasurement}
                                    onChange={setSelectedMeasurement}
                                    options={MEASUREMENT_TYPES}
                                    style={{ width: 200 }}
                                />
                            </Space>
                        </Col>
                        <Col xs={24} sm={24} md={8}>
                            <Button type="primary">
                                <TextField value="Anwenden" />
                            </Button>
                        </Col>
                    </Row>

                    {/* Chart */}
                    <Row>
                        <Col span={24}>
                            {chartData.length > 0 ? (
                                <LineChart
                                    chartConfig={{
                                        data: chartData,
                                        xField: "date",
                                        yField: "value",
                                        height: 400,
                                        xAxis: {
                                            title: {
                                                text: "Datum"
                                            }
                                        },
                                        yAxis: {
                                            title: {
                                                text: yAxisTitle
                                            }
                                        },
                                        smooth: true,
                                        point: {
                                            size: 4,
                                            shape: "circle"
                                        },
                                        tooltip: {
                                            formatter: datum => ({
                                                name: measurementInfo?.label || "Value",
                                                value: `${datum.value.toFixed(selectedMeasurement === "safetyScore" ? 2 : 1)}${
                                                    selectedMeasurement.includes("Capacity")
                                                        ? " kWh"
                                                        : selectedMeasurement === "soh" || selectedMeasurement === "soc"
                                                        ? "%"
                                                        : ""
                                                } (${dayjs(datum.date).format("DD.MM.YYYY")})`
                                            })
                                        },
                                        loading: false
                                    }}
                                />
                            ) : (
                                <div style={{ textAlign: "center", padding: "40px 0", color: "#999" }}>
                                    <TextField value="t_no_measurement_data_available" />
                                </div>
                            )}
                        </Col>
                    </Row>
                </Space>
            </Card>
        </Col>
    );
};

const BatteryStatusView = () => {
    const { params, setParams } = useParams({ options: [{ name: "battery", persist: "site" }] });

    return (
        <Row gutter={[10, 10]}>
            <Col span={24}>
                <Card>
                    <BatterySelector value={params.battery} onChange={battery => setParams({ ...params, battery })} />
                </Card>
            </Col>

            {params.battery ? (
                <Col span={24}>
                    <Row gutter={[10, 10]}>
                        <Col md={24} lg={8}>
                            <StatusBoxWidget
                                title="SOH"
                                request={{
                                    url: datalayerUrls.batteries.values(),
                                    params: { type: "soh", batteryId: params.battery }
                                }}
                                extractData="value"
                                extractLastUpdate="timestamp"
                                renderValue={value => <NumberField value={value} suffix="%" decimals={1} />}
                                icon={value => {
                                    return value >= 90
                                        ? ["fas", "battery-full"]
                                        : value >= 75
                                        ? ["fas", "battery-three-quarters"]
                                        : value >= 50
                                        ? ["fas", "battery-half"]
                                        : value >= 25
                                        ? ["fas", "battery-quarter"]
                                        : ["fas", "battery-empty"];
                                }}
                                queryProps={{ refetchInterval: REFETCH_INTERVAL }}
                            />
                        </Col>
                        <Col md={24} lg={8}>
                            <StatusBoxWidget
                                title="Safety Score"
                                request={{
                                    url: datalayerUrls.batteries.values(),
                                    params: { type: "safetyScore", batteryId: params.battery }
                                }}
                                extractData="value"
                                extractLastUpdate="timestamp"
                                renderValue={value => <NumberField value={value} decimals={2} />}
                                icon={value => {
                                    return value >= 0.8
                                        ? ["fas", "shield-check"]
                                        : value >= 0.5
                                        ? ["fas", "shield-exclamation"]
                                        : ["fas", "shield-xmark"];
                                }}
                                queryProps={{ refetchInterval: REFETCH_INTERVAL }}
                            />
                        </Col>
                        <Col md={24} lg={8}>
                            <StatusBoxWidget
                                title="SOC"
                                request={{
                                    url: datalayerUrls.batteries.values(),
                                    params: { type: "soc", batteryId: params.battery }
                                }}
                                extractData="value"
                                extractLastUpdate="timestamp"
                                renderValue={value => <NumberField value={value} suffix="%" />}
                                icon={value => {
                                    return value >= 99
                                        ? ["fas", "battery-full"]
                                        : value >= 75
                                        ? ["fas", "battery-three-quarters"]
                                        : value >= 50
                                        ? ["fas", "battery-half"]
                                        : value >= 25
                                        ? ["fas", "battery-quarter"]
                                        : ["fas", "battery-empty"];
                                }}
                                queryProps={{ refetchInterval: REFETCH_INTERVAL }}
                            />
                        </Col>

                        {/* <Col md={24} lg={8}>
                            <StatusBoxWidget
                                title="Ladezustand"
                                request={{
                                    url: datalayerUrls.batteries.values(),
                                    params: { type: "chargeState", batteryId: params.battery }
                                }}
                                extractData="value"
                                extractLastUpdate="timestamp"
                                renderValue={value => <TextField value={value} />}
                                icon={["fas", "charging-station"]}
                                queryProps={{ refetchInterval: REFETCH_INTERVAL }}
                            />
                        </Col> */}

                        <Col md={24} lg={8}>
                            <StatusBoxWidget
                                title="Nennkapazität"
                                request={{
                                    url: datalayerUrls.batteries.values(),
                                    params: { type: "nominalCapacity", batteryId: params.battery }
                                }}
                                extractData="value"
                                extractLastUpdate="timestamp"
                                renderValue={value => <NumberField value={value} suffix="kWh" decimals={1} />}
                                icon={["fas", "battery-bolt"]}
                                queryProps={{ refetchInterval: REFETCH_INTERVAL }}
                            />
                        </Col>

                        <Col md={24} lg={8}>
                            <StatusBoxWidget
                                title="Realkapazität"
                                request={{
                                    url: datalayerUrls.batteries.values(),
                                    params: { type: "realCapacity", batteryId: params.battery }
                                }}
                                extractData="value"
                                extractLastUpdate="timestamp"
                                renderValue={value => <NumberField value={value} suffix="kWh" decimals={1} />}
                                icon={["fas", "battery-bolt"]}
                                queryProps={{ refetchInterval: REFETCH_INTERVAL }}
                            />
                        </Col>

                        <Col md={24} lg={8}>
                            <StatusBoxWidget
                                title="VIN"
                                request={{
                                    url: datalayerUrls.batteries.values(),
                                    params: { type: "currentVin", batteryId: params.battery }
                                }}
                                extractData="value"
                                renderValue={value => <TextField value={value} />}
                                icon={["fas", "car"]}
                            />
                        </Col>
                    </Row>
                </Col>
            ) : null}

            {params.battery ? (
                <BatteriemessungenCard batteryId={params.battery} />
            ) : (
                <Col span={24}>
                    <Card height={500}>
                        <Result type="noBatterySelected" />
                    </Card>
                </Col>
            )}
        </Row>
    );
};

const BmBatteryComponent = () => {
    return <BatteryStatusView />;
};

export default BmBatteryComponent;
