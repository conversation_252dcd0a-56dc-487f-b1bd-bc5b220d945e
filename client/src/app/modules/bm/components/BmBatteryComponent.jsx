import { Row, Col } from "antd";
import React from "react";

import { Card } from "misc/card";
import { Result } from "misc/results";
import { useParams } from "misc/hooks";
import { BatterySelector } from "misc/selectors";
import { TextField, NumberField } from "misc/fields";
import { datalayerUrls } from "misc/urls";
import { StatusBoxWidget } from "misc/widgets/consumers";

const REFETCH_INTERVAL = 30000; // 30 seconds

const BatteryStatusView = () => {
    const { params, setParams } = useParams({ options: [{ name: "battery", persist: "site" }] });

    return (
        <Row gutter={[10, 10]}>
            <Col span={24}>
                <Card>
                    <BatterySelector value={params.battery} onChange={battery => setParams({ ...params, battery })} />
                </Card>
            </Col>

            {params.battery ? (
                <Col span={24}>
                    <Row gutter={[10, 10]}>
                        <Col md={24} lg={8}>
                            <StatusBoxWidget
                                title="SOH"
                                request={{
                                    url: datalayerUrls.batteries.values(),
                                    params: { type: "soh", batteryId: params.battery }
                                }}
                                extractData="value"
                                extractLastUpdate="timestamp"
                                renderValue={value => <NumberField value={value} suffix="%" decimals={1} />}
                                icon={value => {
                                    return value >= 90
                                        ? ["fas", "battery-full"]
                                        : value >= 75
                                        ? ["fas", "battery-three-quarters"]
                                        : value >= 50
                                        ? ["fas", "battery-half"]
                                        : value >= 25
                                        ? ["fas", "battery-quarter"]
                                        : ["fas", "battery-empty"];
                                }}
                                queryProps={{ refetchInterval: REFETCH_INTERVAL }}
                            />
                        </Col>
                        <Col md={24} lg={8}>
                            <StatusBoxWidget
                                title="Safety Score"
                                request={{
                                    url: datalayerUrls.batteries.values(),
                                    params: { type: "safetyScore", batteryId: params.battery }
                                }}
                                extractData="value"
                                extractLastUpdate="timestamp"
                                renderValue={value => <NumberField value={value} decimals={2} />}
                                icon={value => {
                                    return value >= 0.8
                                        ? ["fas", "shield-check"]
                                        : value >= 0.5
                                        ? ["fas", "shield-exclamation"]
                                        : ["fas", "shield-xmark"];
                                }}
                                queryProps={{ refetchInterval: REFETCH_INTERVAL }}
                            />
                        </Col>
                        <Col md={24} lg={8}>
                            <StatusBoxWidget
                                title="SOC"
                                request={{
                                    url: datalayerUrls.batteries.values(),
                                    params: { type: "soc", batteryId: params.battery }
                                }}
                                extractData="value"
                                extractLastUpdate="timestamp"
                                renderValue={value => <NumberField value={value} suffix="%" />}
                                icon={value => {
                                    return value >= 99
                                        ? ["fas", "battery-full"]
                                        : value >= 75
                                        ? ["fas", "battery-three-quarters"]
                                        : value >= 50
                                        ? ["fas", "battery-half"]
                                        : value >= 25
                                        ? ["fas", "battery-quarter"]
                                        : ["fas", "battery-empty"];
                                }}
                                queryProps={{ refetchInterval: REFETCH_INTERVAL }}
                            />
                        </Col>

                        <Col md={24} lg={8}>
                            <StatusBoxWidget
                                title="Ladezustand"
                                request={{
                                    url: datalayerUrls.batteries.values(),
                                    params: { type: "chargeState", batteryId: params.battery }
                                }}
                                extractData="value"
                                extractLastUpdate="timestamp"
                                renderValue={value => <TextField value={value} />}
                                icon={["fas", "charging-station"]}
                                queryProps={{ refetchInterval: REFETCH_INTERVAL }}
                            />
                        </Col>

                        <Col md={24} lg={8}>
                            <StatusBoxWidget
                                title="Nennkapazität"
                                request={{
                                    url: datalayerUrls.batteries.values(),
                                    params: { type: "nominalCapacity", batteryId: params.battery }
                                }}
                                extractData="value"
                                extractLastUpdate="timestamp"
                                renderValue={value => <NumberField value={value} suffix="kWh" decimals={1} />}
                                icon={["fas", "battery-bolt"]}
                                queryProps={{ refetchInterval: REFETCH_INTERVAL }}
                            />
                        </Col>

                        <Col md={24} lg={8}>
                            <StatusBoxWidget
                                title="Realkapazität"
                                request={{
                                    url: datalayerUrls.batteries.values(),
                                    params: { type: "realCapacity", batteryId: params.battery }
                                }}
                                extractData="value"
                                extractLastUpdate="timestamp"
                                renderValue={value => <NumberField value={value} suffix="kWh" decimals={1} />}
                                icon={["fas", "battery-bolt"]}
                                queryProps={{ refetchInterval: REFETCH_INTERVAL }}
                            />
                        </Col>

                        <Col md={24} lg={8}>
                            <StatusBoxWidget
                                title="VIN"
                                request={{
                                    url: datalayerUrls.batteries.values(),
                                    params: { type: "currentVin", batteryId: params.battery }
                                }}
                                extractData="value"
                                renderValue={value => <TextField value={value} />}
                                icon={["fas", "car"]}
                            />
                        </Col>
                    </Row>
                </Col>
            ) : (
                <Col span={24}>
                    <Card height={500}>
                        <Result type="noBatterySelected" />
                    </Card>
                </Col>
            )}
        </Row>
    );
};

const BmBatteryComponent = () => {
    return <BatteryStatusView />;
};

export default BmBatteryComponent;
